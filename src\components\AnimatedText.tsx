import React from 'react';
import { useCurrentFrame, interpolate } from 'remotion';

interface AnimatedTextProps {
  text: string;
  startFrame: number;
  className?: string;
  animationType?: 'reveal' | 'fade' | 'typewriter';
  duration?: number;
  delay?: number;
}

export const AnimatedText: React.FC<AnimatedTextProps> = ({
  text,
  startFrame,
  className = '',
  animationType = 'reveal',
  duration = 60,
  delay = 0,
}) => {
  const frame = useCurrentFrame();

  const actualStartFrame = startFrame + delay;
  const progress = interpolate(
    frame,
    [actualStartFrame, actualStartFrame + duration],
    [0, 1],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  if (animationType === 'fade') {
    const opacity = interpolate(progress, [0, 1], [0, 1]);
    return (
      <div
        className={className}
        style={{ opacity }}
      >
        {text}
      </div>
    );
  }

  if (animationType === 'typewriter') {
    const charactersToShow = Math.floor(progress * text.length);
    const visibleText = text.slice(0, charactersToShow);
    const showCursor = progress < 1;

    return (
      <div className={className}>
        {visibleText}
        {showCursor && <span className="animate-pulse">|</span>}
      </div>
    );
  }

  // Default: reveal animation with split text effect
  const words = text.split(' ');

  return (
    <div className={className}>
      {words.map((word, wordIndex) => {
        const wordProgress = interpolate(
          progress,
          [wordIndex / words.length, (wordIndex + 1) / words.length],
          [0, 1],
          { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
        );

        const opacity = interpolate(wordProgress, [0, 1], [0, 1]);
        const translateY = interpolate(wordProgress, [0, 1], [20, 0]);

        return (
          <span
            key={wordIndex}
            style={{
              opacity,
              transform: `translateY(${translateY}px)`,
              display: 'inline-block',
              marginRight: wordIndex < words.length - 1 ? '0.25rem' : '0',
            }}
          >
            {word}
          </span>
        );
      })}
    </div>
  );
};

export default AnimatedText;
