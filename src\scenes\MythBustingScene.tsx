import React from 'react';
import { AbsoluteFill, useCurrentFrame, interpolate } from 'remotion';
import AnimatedText from '../components/AnimatedText';
import DataChart from '../components/DataChart';
import { LightRays } from '../components/LightRays';
import { SceneProps } from '../types';

export const MythBustingScene: React.FC<SceneProps> = ({ frame, durationInFrames }) => {
  const currentFrame = useCurrentFrame();

  // 影响因素数据
  const factorsData = [
    { label: '睡眠质量', value: 35, color: '#8B5CF6' },
    { label: '睡眠时长', value: 25, color: '#06B6D4' },
    { label: '生活方式', value: 25, color: '#10B981' },
    { label: '入睡时间', value: 15, color: '#F59E0B' },
  ];

  const factors = [
    {
      icon: '💤',
      title: '睡眠质量',
      subtitle: '是否深度睡眠足够',
      description: '即使早睡，如果睡眠浅、频繁被打断，依旧会疲惫',
      startFrame: 80,
      color: 'from-purple-600 to-purple-400'
    },
    {
      icon: '⏰',
      title: '睡眠总时长',
      subtitle: '补觉不能完全抵消熬夜',
      description: '睡眠债务的累积效应无法通过偶尔的长时间睡眠完全消除',
      startFrame: 140,
      color: 'from-cyan-600 to-cyan-400'
    },
    {
      icon: '🏃‍♂️',
      title: '生活方式',
      subtitle: '饮食、运动、压力管理同样关键',
      description: '健康是一个系统工程，单一因素无法决定整体状态',
      startFrame: 200,
      color: 'from-green-600 to-green-400'
    }
  ];

  return (
    <AbsoluteFill className="bg-gradient-to-br from-red-900 via-orange-900 to-yellow-900">
      {/* Light Rays 背景效果 */}
      <LightRays
        raysOrigin="top-left"
        raysColor="#F59E0B"
        raysSpeed={1.8}
        lightSpread={0.8}
        rayLength={1.4}
        noiseAmount={0.04}
        distortion={0.02}
        fadeDistance={0.6}
        saturation={0.8}
        className="opacity-60"
      />
      {/* 标题 */}
      <div className="absolute top-16 left-1/2 transform -translate-x-1/2 text-center">
        <AnimatedText
          text="为什么说早睡改命是伪命题"
          startFrame={0}
          className="text-5xl font-bold text-white mb-4"
          animationType="reveal"
          duration={60}
        />
        
        <AnimatedText
          text="把早睡当成改变命运的钥匙，就忽略了其他重要因素"
          startFrame={40}
          className="text-2xl text-gray-200"
          animationType="fade"
          duration={50}
        />
      </div>

      {/* 主要内容区域 */}
      <div className="flex items-start justify-center h-full pt-40 pb-16">
        <div className="grid grid-cols-2 gap-12 max-w-7xl w-full px-8">
          {/* 左侧：影响因素饼图 */}
          <div 
            className="bg-white/10 backdrop-blur-sm rounded-2xl p-8"
            style={{
              opacity: interpolate(currentFrame, [60, 90], [0, 1], {
                extrapolateLeft: 'clamp',
                extrapolateRight: 'clamp',
              }),
              transform: `scale(${interpolate(
                currentFrame,
                [60, 90],
                [0.8, 1],
                { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
              )})`,
            }}
          >
            <DataChart
              data={factorsData}
              startFrame={90}
              type="pie"
              width={400}
              height={300}
              title="健康睡眠的影响因素"
            />
            
            <div className="mt-6 text-center">
              <AnimatedText
                text="早睡只占15%的影响"
                startFrame={150}
                className="text-xl font-bold text-yellow-300"
                animationType="fade"
                duration={40}
              />
            </div>
          </div>

          {/* 右侧：详细因素说明 */}
          <div className="space-y-6">
            {factors.map((factor, index) => (
              <div
                key={index}
                className="bg-white/10 backdrop-blur-sm rounded-xl p-6"
                style={{
                  opacity: interpolate(
                    currentFrame,
                    [factor.startFrame, factor.startFrame + 30],
                    [0, 1],
                    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
                  ),
                  transform: `translateX(${interpolate(
                    currentFrame,
                    [factor.startFrame, factor.startFrame + 30],
                    [50, 0],
                    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
                  )}px)`,
                }}
              >
                <div className="flex items-start space-x-4">
                  <div className="text-4xl">{factor.icon}</div>
                  <div className="flex-1">
                    <AnimatedText
                      text={factor.title}
                      startFrame={factor.startFrame + 20}
                      className="text-2xl font-bold text-white mb-2"
                      animationType="fade"
                      duration={30}
                    />
                    
                    <AnimatedText
                      text={factor.subtitle}
                      startFrame={factor.startFrame + 35}
                      className="text-lg text-yellow-300 font-semibold mb-3"
                      animationType="fade"
                      duration={30}
                    />
                    
                    <AnimatedText
                      text={factor.description}
                      startFrame={factor.startFrame + 50}
                      className="text-base text-gray-200 leading-relaxed"
                      animationType="typewriter"
                      duration={60}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 对比示例 */}
      <div 
        className="absolute bottom-32 left-1/2 transform -translate-x-1/2 w-full max-w-6xl px-8"
        style={{
          opacity: interpolate(currentFrame, [320, 350], [0, 1], {
            extrapolateLeft: 'clamp',
            extrapolateRight: 'clamp',
          }),
        }}
      >
        <div className="grid grid-cols-2 gap-8">
          {/* 错误示例 */}
          <div className="bg-red-500/20 border-2 border-red-500 rounded-xl p-6">
            <div className="text-center mb-4">
              <span className="text-3xl">❌</span>
              <h3 className="text-xl font-bold text-red-300 mt-2">错误认知</h3>
            </div>
            <AnimatedText
              text="早睡 + 浅睡眠 + 不规律 + 高压力 = 依然疲惫"
              startFrame={350}
              className="text-lg text-white text-center"
              animationType="typewriter"
              duration={60}
            />
          </div>

          {/* 正确示例 */}
          <div className="bg-green-500/20 border-2 border-green-500 rounded-xl p-6">
            <div className="text-center mb-4">
              <span className="text-3xl">✅</span>
              <h3 className="text-xl font-bold text-green-300 mt-2">正确理解</h3>
            </div>
            <AnimatedText
              text="规律作息 + 深度睡眠 + 健康生活方式 = 真正健康"
              startFrame={380}
              className="text-lg text-white text-center"
              animationType="typewriter"
              duration={60}
            />
          </div>
        </div>
      </div>

      {/* 结论 */}
      <div 
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-center"
        style={{
          opacity: interpolate(currentFrame, [480, 510], [0, 1], {
            extrapolateLeft: 'clamp',
            extrapolateRight: 'clamp',
          }),
        }}
      >
        <AnimatedText
          text="所以，早睡不是改命，而是健康生活的一部分"
          startFrame={510}
          className="text-2xl font-bold text-yellow-300 max-w-4xl"
          animationType="typewriter"
          duration={80}
        />
      </div>
    </AbsoluteFill>
  );
};

export default MythBustingScene;
