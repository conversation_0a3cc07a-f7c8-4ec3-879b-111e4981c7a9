import React from 'react';
import { AbsoluteFill, useCurrentFrame, interpolate } from 'remotion';
import AnimatedText from '../components/AnimatedText';
import { LightRays } from '../components/LightRays';
import { SceneProps } from '../types';

export const EarlySleepBenefitsScene: React.FC<SceneProps> = ({ frame, durationInFrames }) => {
  const currentFrame = useCurrentFrame();

  // 益处数据
  const benefits = [
    {
      icon: '🌙',
      title: '褪黑素分泌',
      description: '通常在晚上 9–11 点开始增加，过晚入睡可能打乱节律',
      startFrame: 60,
      color: 'from-purple-500 to-blue-500'
    },
    {
      icon: '🧠',
      title: '学习和记忆',
      description: '研究发现，较早入睡的人，第二天的专注力和工作效率更高',
      startFrame: 120,
      color: 'from-blue-500 to-cyan-500'
    },
    {
      icon: '💚',
      title: '心理健康',
      description: '长期熬夜会增加焦虑和抑郁风险',
      startFrame: 180,
      color: 'from-green-500 to-teal-500'
    }
  ];

  return (
    <AbsoluteFill className="bg-gradient-to-br from-green-900 via-teal-900 to-blue-900">
      <LightRays
        raysOrigin="bottom-left"
        raysColor="#10B981"
        raysSpeed={1.0}
        lightSpread={0.8}
        rayLength={1.3}
        noiseAmount={0.04}
        distortion={0.04}
        fadeDistance={0.6}
        saturation={0.8}
        className="opacity-75"
      />
      {/* 标题 */}
      <div className="absolute top-16 left-1/2 transform -translate-x-1/2 text-center">
        <AnimatedText
          text="早睡的确有益处，但并非唯一关键"
          startFrame={0}
          className="text-5xl font-bold text-white mb-6"
          animationType="reveal"
          duration={60}
        />
      </div>

      {/* 主要内容 */}
      <div className="flex flex-col items-center justify-center h-full pt-32 pb-16 space-y-12">
        {benefits.map((benefit, index) => (
          <div
            key={index}
            className="w-full max-w-5xl"
            style={{
              opacity: interpolate(
                currentFrame,
                [benefit.startFrame, benefit.startFrame + 30],
                [0, 1],
                { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
              ),
              transform: `translateX(${interpolate(
                currentFrame,
                [benefit.startFrame, benefit.startFrame + 30],
                [index % 2 === 0 ? -100 : 100, 0],
                { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
              )}px)`,
            }}
          >
            <div className={`bg-gradient-to-r ${benefit.color} p-1 rounded-2xl`}>
              <div className="bg-black/20 backdrop-blur-sm rounded-2xl p-8">
                <div className="flex items-center space-x-6">
                  {/* 图标 */}
                  <div className="text-6xl">{benefit.icon}</div>
                  
                  {/* 内容 */}
                  <div className="flex-1">
                    <AnimatedText
                      text={benefit.title}
                      startFrame={benefit.startFrame + 20}
                      className="text-3xl font-bold text-white mb-4"
                      animationType="fade"
                      duration={40}
                    />
                    
                    <AnimatedText
                      text={benefit.description}
                      startFrame={benefit.startFrame + 40}
                      className="text-xl text-gray-200 leading-relaxed"
                      animationType="typewriter"
                      duration={80}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 时间轴可视化 */}
      <div 
        className="absolute bottom-32 left-1/2 transform -translate-x-1/2 w-full max-w-6xl px-8"
        style={{
          opacity: interpolate(currentFrame, [300, 330], [0, 1], {
            extrapolateLeft: 'clamp',
            extrapolateRight: 'clamp',
          }),
        }}
      >
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8">
          <AnimatedText
            text="褪黑素分泌时间轴"
            startFrame={330}
            className="text-2xl font-bold text-white text-center mb-6"
            animationType="fade"
            duration={30}
          />
          
          {/* 时间轴 */}
          <div className="relative">
            <div className="flex justify-between items-center">
              {['18:00', '21:00', '23:00', '01:00', '03:00', '06:00'].map((time, index) => {
                const isOptimal = index >= 1 && index <= 2; // 21:00-23:00 最佳时间
                return (
                  <div key={time} className="flex flex-col items-center">
                    <div 
                      className={`w-4 h-4 rounded-full ${
                        isOptimal ? 'bg-green-400' : 'bg-gray-400'
                      } mb-2`}
                      style={{
                        transform: `scale(${interpolate(
                          currentFrame,
                          [360 + index * 10, 360 + index * 10 + 20],
                          [0, 1],
                          { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
                        )})`,
                      }}
                    />
                    <span className="text-sm text-white">{time}</span>
                    {isOptimal && (
                      <span className="text-xs text-green-300 mt-1">最佳</span>
                    )}
                  </div>
                );
              })}
            </div>
            
            {/* 连接线 */}
            <div 
              className="absolute top-2 left-0 h-0.5 bg-gradient-to-r from-gray-400 via-green-400 to-gray-400"
              style={{
                width: `${interpolate(
                  currentFrame,
                  [360, 420],
                  [0, 100],
                  { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
                )}%`,
              }}
            />
          </div>
        </div>
      </div>

      {/* 结论 */}
      <div 
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-center"
        style={{
          opacity: interpolate(currentFrame, [480, 510], [0, 1], {
            extrapolateLeft: 'clamp',
            extrapolateRight: 'clamp',
          }),
        }}
      >
        <AnimatedText
          text="所以，早睡确实对身体有好处，尤其是和昼夜节律保持一致的时候"
          startFrame={510}
          className="text-2xl font-semibold text-yellow-300 max-w-4xl"
          animationType="typewriter"
          duration={100}
        />
      </div>
    </AbsoluteFill>
  );
};

export default EarlySleepBenefitsScene;
