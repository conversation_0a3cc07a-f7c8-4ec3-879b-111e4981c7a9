{"name": "2025082401_blank", "version": "1.0.0", "description": "My Remotion video", "repository": {}, "license": "UNLICENSED", "private": true, "dependencies": {"@gsap/react": "^2.1.2", "@react-three/drei": "^10.7.4", "@react-three/fiber": "^9.3.0", "@react-three/postprocessing": "^3.0.4", "@react-three/rapier": "^2.1.0", "@remotion/cli": "4.0.340", "@remotion/tailwind-v4": "4.0.340", "gl-matrix": "^3.4.4", "gsap": "^3.13.0", "lenis": "^1.3.9", "mathjs": "^14.6.0", "meshline": "^3.3.1", "motion": "^12.23.12", "ogl": "^1.0.11", "postprocessing": "^6.37.7", "react": "19.0.0", "react-dom": "19.0.0", "react-icons": "^5.5.0", "remotion": "4.0.340", "tailwindcss": "4.0.0", "three": "^0.179.1"}, "devDependencies": {"@remotion/eslint-config-flat": "4.0.340", "@types/matter-js": "^0.20.0", "@types/react": "19.0.0", "@types/three": "^0.179.0", "@types/web": "0.0.166", "eslint": "9.19.0", "prettier": "3.6.0", "typescript": "5.8.2"}, "scripts": {"dev": "remotion studio", "build": "remotion bundle", "upgrade": "remotion upgrade", "lint": "eslint src && tsc"}, "sideEffects": ["*.css"]}