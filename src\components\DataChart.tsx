import React from 'react';
import { useCurrentFrame, interpolate } from 'remotion';
import { DataVisualizationProps } from '../types';

interface DataChartProps extends DataVisualizationProps {
  startFrame: number;
  type: 'bar' | 'pie' | 'line';
  width?: number;
  height?: number;
  title?: string;
}

export const DataChart: React.FC<DataChartProps> = ({
  data,
  startFrame,
  type = 'bar',
  width = 400,
  height = 300,
  title,
  animationDelay = 0,
}) => {
  const frame = useCurrentFrame();
  
  const animationProgress = interpolate(
    frame,
    [startFrame + animationDelay, startFrame + animationDelay + 60],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
    }
  );

  if (type === 'bar') {
    const maxValue = Math.max(...data.map(d => d.value));
    
    return (
      <div className="flex flex-col items-center space-y-4">
        {title && (
          <h3 className="text-xl font-bold text-white">{title}</h3>
        )}
        <div 
          className="flex items-end space-x-2 p-4 bg-gray-800 rounded-lg"
          style={{ width, height }}
        >
          {data.map((item, index) => {
            const barHeight = (item.value / maxValue) * (height - 80);
            const animatedHeight = barHeight * animationProgress;
            
            return (
              <div key={index} className="flex flex-col items-center space-y-2">
                <div
                  className="transition-all duration-300 rounded-t"
                  style={{
                    width: Math.max(40, (width - 40) / data.length - 8),
                    height: animatedHeight,
                    backgroundColor: item.color || '#3B82F6',
                  }}
                />
                <span className="text-xs text-gray-300 text-center">
                  {item.label}
                </span>
                <span className="text-sm font-bold text-white">
                  {Math.round(item.value * animationProgress)}
                </span>
              </div>
            );
          })}
        </div>
      </div>
    );
  }

  if (type === 'pie') {
    const total = data.reduce((sum, item) => sum + item.value, 0);
    let currentAngle = 0;
    
    return (
      <div className="flex flex-col items-center space-y-4">
        {title && (
          <h3 className="text-xl font-bold text-white">{title}</h3>
        )}
        <div className="relative">
          <svg width={width} height={height} className="transform -rotate-90">
            {data.map((item, index) => {
              const percentage = item.value / total;
              const angle = percentage * 360 * animationProgress;
              const radius = Math.min(width, height) / 2 - 20;
              const centerX = width / 2;
              const centerY = height / 2;
              
              const startAngle = currentAngle;
              const endAngle = currentAngle + angle;
              currentAngle += percentage * 360;
              
              const startX = centerX + radius * Math.cos((startAngle * Math.PI) / 180);
              const startY = centerY + radius * Math.sin((startAngle * Math.PI) / 180);
              const endX = centerX + radius * Math.cos((endAngle * Math.PI) / 180);
              const endY = centerY + radius * Math.sin((endAngle * Math.PI) / 180);
              
              const largeArcFlag = angle > 180 ? 1 : 0;
              
              const pathData = [
                `M ${centerX} ${centerY}`,
                `L ${startX} ${startY}`,
                `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${endX} ${endY}`,
                'Z'
              ].join(' ');
              
              return (
                <path
                  key={index}
                  d={pathData}
                  fill={item.color || `hsl(${index * 60}, 70%, 50%)`}
                  stroke="white"
                  strokeWidth="2"
                />
              );
            })}
          </svg>
          <div className="absolute inset-0 flex flex-col items-center justify-center">
            {data.map((item, index) => (
              <div key={index} className="text-xs text-white text-center">
                <span className="font-bold">{item.label}</span>
                <br />
                <span>{Math.round((item.value / total) * 100 * animationProgress)}%</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return null;
};

export default DataChart;
