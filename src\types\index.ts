// 视频场景类型定义
export interface SceneProps {
  frame: number;
  durationInFrames: number;
}

// 文本动画配置
export interface TextAnimationConfig {
  delay?: number;
  duration?: number;
  stagger?: number;
}

// 数据可视化配置
export interface DataVisualizationProps {
  data: Array<{
    label: string;
    value: number;
    color?: string;
  }>;
  animationDelay?: number;
}

// 睡眠数据类型
export interface SleepData {
  hours: number;
  quality: 'poor' | 'fair' | 'good' | 'excellent';
  regularity: number; // 0-100
}

// 主题颜色配置
export interface ThemeColors {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  text: string;
}
