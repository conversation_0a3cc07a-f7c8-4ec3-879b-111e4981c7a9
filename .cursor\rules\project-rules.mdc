---
description: Project rules for Remotion video project with React, TypeScript, Tailwind CSS, and react-bits components
globs: ["**/*.{ts,tsx,js,jsx,css,json,md}"]
alwaysApply: true
---

# Project Rules for Remotion + React-bits

## Project Overview
This is a Remotion video project using React 19, TypeScript, Tailwind CSS v4, and react-bits component library for enhanced UI animations and effects.

## Technology Stack
- **Framework**: Remotion 4.0.340 for video generation
- **Frontend**: React 19 with TypeScript 5.8.2
- **Styling**: Tailwind CSS v4 with @remotion/tailwind-v4
- **Components**: react-bits for advanced animations and UI components
- **Linting**: ESLint with @remotion/eslint-config-flat
- **Formatting**: Prettier 3.6.0

## Code Style Guidelines

### TypeScript/React
- Use TypeScript for all new files (.tsx for React components, .ts for utilities)
- Prefer functional components with hooks over class components
- Use proper TypeScript types and interfaces, avoid `any`
- Export components as default exports for main components
- Use named exports for utilities and helper functions

### Component Structure
```typescript
// Preferred component structure
import React from 'react';
import { AbsoluteFill } from 'remotion';

interface ComponentProps {
  // Define props with proper types
}

export const ComponentName: React.FC<ComponentProps> = ({ prop1, prop2 }) => {
  return (
    <AbsoluteFill className="...">
      {/* Component content */}
    </AbsoluteFill>
  );
};

export default ComponentName;
```

### React-bits Integration
- Import react-bits components from the library when available
- Use react-bits for animations, text effects, backgrounds, and interactive components
- Prefer react-bits components over custom implementations for common UI patterns
- Categories available:
  - **TextAnimations**: For text effects and typography animations
  - **Animations**: For interactive animations and cursor effects
  - **Components**: For complete UI components with advanced interactions
  - **Backgrounds**: For immersive background effects

### Remotion Specific
- Use `AbsoluteFill` for full-screen layouts
- Use `useCurrentFrame()` and `useVideoConfig()` for frame-based animations
- Prefer `interpolate()` for smooth value transitions
- Use `Sequence` for timing different parts of the composition
- Keep compositions in separate files and register them in `src/Root.tsx`

### Styling with Tailwind CSS
- Use Tailwind CSS v4 classes for styling
- Prefer utility classes over custom CSS when possible
- Use responsive design patterns with Tailwind breakpoints
- Leverage Tailwind's animation utilities alongside react-bits components
- Custom CSS should be minimal and placed in `src/index.css`

### File Organization
```
src/
├── components/          # Reusable React components
├── compositions/        # Remotion composition files
├── utils/              # Utility functions and helpers
├── types/              # TypeScript type definitions
├── assets/             # Static assets (images, fonts, etc.)
├── Composition.tsx     # Main composition
├── Root.tsx           # Remotion root with composition registry
└── index.ts           # Entry point
```

### Naming Conventions
- **Files**: PascalCase for components (`MyComponent.tsx`), camelCase for utilities (`myUtil.ts`)
- **Components**: PascalCase (`MyComponent`)
- **Variables/Functions**: camelCase (`myVariable`, `myFunction`)
- **Constants**: UPPER_SNAKE_CASE (`MY_CONSTANT`)
- **Types/Interfaces**: PascalCase with descriptive names (`UserData`, `ComponentProps`)

### Performance Guidelines
- Use React.memo() for components that don't need frequent re-renders
- Optimize heavy computations with useMemo() and useCallback()
- Be mindful of frame rate - avoid expensive operations in render loops
- Use react-bits components which are optimized for performance
- Lazy load heavy assets when possible

### Error Handling
- Use proper error boundaries for React components
- Handle async operations with proper try-catch blocks
- Provide meaningful error messages for debugging
- Use TypeScript strict mode to catch errors at compile time

### Testing Considerations
- Write unit tests for utility functions
- Test component rendering with different props
- Test Remotion compositions at different frame positions
- Ensure react-bits components integrate properly with Remotion

### Dependencies Management
- Keep dependencies up to date, especially Remotion and react-bits
- Use exact versions for critical dependencies
- Prefer peer dependencies when possible to avoid version conflicts
- Document any specific version requirements

### Git Workflow
- Use conventional commit messages
- Keep commits focused and atomic
- Use meaningful branch names
- Include tests with new features

## Common Patterns

### Animation with Remotion + react-bits
```typescript
import { useCurrentFrame, interpolate } from 'remotion';
import { SomeReactBitsComponent } from 'react-bits';

export const AnimatedComponent = () => {
  const frame = useCurrentFrame();
  const opacity = interpolate(frame, [0, 30], [0, 1]);

  return (
    <div style={{ opacity }}>
      <SomeReactBitsComponent />
    </div>
  );
};
```

### Responsive Design
```typescript
// Use Tailwind responsive classes with react-bits
<ReactBitsComponent className="w-full md:w-1/2 lg:w-1/3" />
```

### Type-safe Props
```typescript
interface AnimationProps {
  duration: number;
  delay?: number;
  children: React.ReactNode;
}

export const Animation: React.FC<AnimationProps> = ({
  duration,
  delay = 0,
  children
}) => {
  // Implementation
};
```

## Best Practices
1. **Performance**: Always consider the impact on render performance
2. **Accessibility**: Ensure animations don't cause motion sickness
3. **Responsiveness**: Test on different screen sizes
4. **Type Safety**: Use TypeScript features to prevent runtime errors
5. **Code Reuse**: Create reusable components and utilities
6. **Documentation**: Comment complex logic and animation sequences
7. **react-bits Integration**: Leverage the library's optimized components instead of reinventing
8. **Remotion Optimization**: Use Remotion's built-in optimization features
