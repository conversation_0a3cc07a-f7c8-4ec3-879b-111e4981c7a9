import "./index.css";
import { Composition } from "remotion";
import { MyComposition } from "./Composition";

export const RemotionRoot: React.FC = () => {
  const fps = 30;
  // 总时长：8 + 20 + 20 + 20 + 27 = 95秒
  const totalDurationInFrames = 95 * fps; // 2850帧

  return (
    <>
      <Composition
        id="EarlySleepMyth"
        component={MyComposition}
        durationInFrames={totalDurationInFrames}
        fps={fps}
        width={1920}
        height={1080}
        defaultProps={{}}
      />
    </>
  );
};
