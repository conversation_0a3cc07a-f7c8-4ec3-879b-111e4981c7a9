import React, { useMemo } from 'react';
import { AbsoluteFill, useCurrentFrame, interpolate } from 'remotion';
import AnimatedText from '../components/AnimatedText';
import { LightRays } from '../components/LightRays';
import { SceneProps } from '../types';

export const OpeningScene: React.FC<SceneProps> = ({ frame, durationInFrames }) => {
  const currentFrame = useCurrentFrame();

  // 主标题动画时机
  const titleStartFrame = 30;
  const subtitleStartFrame = 90;
  const questionStartFrame = 150;

  // 计算光线角度的自动旋转
  // 每600帧（20秒）完成一次完整旋转，更慢更平滑
  const rotationSpeed = 600;
  const angle = (currentFrame / rotationSpeed) * 2 * Math.PI;

  // 将角度转换为虚拟鼠标位置（圆形运动）
  // 中心点为(0.5, 0.5)，半径为0.2（减小半径，减少角度变化幅度）
  const radius = 0.2;
  const virtualMousePos = useMemo(() => ({
    x: 0.5 + Math.cos(angle) * radius,
    y: 0.5 + Math.sin(angle) * radius
  }), [angle, radius]);

  return (
    <AbsoluteFill>
      {/* Light Rays 背景效果 */}
      <LightRays
        raysOrigin="top-center"
        raysColor="#00ffff"
        raysSpeed={1.0}
        lightSpread={0.8}
        rayLength={1.2}
        noiseAmount={0.05}
        distortion={0.02}
        fadeDistance={0.6}
        saturation={0.8}
        followMouse={false}
        mouseInfluence={0.2}
        virtualMousePos={virtualMousePos}
        className="opacity-80"
      />

      {/* 主要内容 */}
      <div className="flex flex-col items-center justify-center h-full text-center px-8">
        {/* 主标题 */}
        <AnimatedText
          text="早睡是伪命题吗？"
          startFrame={titleStartFrame}
          className="text-6xl font-bold text-white mb-8"
          animationType="reveal"
          duration={60}
        />

        {/* 副标题 */}
        <AnimatedText
          text="很多人相信：只要早睡，就能变健康、变聪明，甚至改变人生。"
          startFrame={subtitleStartFrame}
          className="text-2xl text-gray-200 mb-6 max-w-4xl leading-relaxed"
          animationType="fade"
          duration={80}
        />

        {/* 引导问题 */}
        <AnimatedText
          text="但问题是，早睡真的是万能的吗？还是一个伪命题？"
          startFrame={questionStartFrame}
          className="text-3xl font-semibold text-yellow-300 max-w-3xl leading-relaxed"
          animationType="typewriter"
          duration={100}
        />
      </div>

      {/* 底部提示 */}
      <div 
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        style={{
          opacity: interpolate(
            currentFrame,
            [durationInFrames - 60, durationInFrames - 30],
            [0, 1],
            {
              extrapolateLeft: 'clamp',
              extrapolateRight: 'clamp',
            }
          ),
        }}
      >
        <div className="text-white/70 text-sm">
          让我们一起探索真相
        </div>
      </div>
    </AbsoluteFill>
  );
};

export default OpeningScene;
